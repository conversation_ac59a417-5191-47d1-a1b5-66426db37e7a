# Scripted effects to prevent AI from joining factions during war

# Effect to completely block faction joining during war
block_faction_joining_during_war = {
	if = {
		limit = {
			has_war = yes
			is_ai = yes
		}
		# Add a temporary modifier that prevents faction joining
		add_timed_idea = {
			idea = no_faction_joining_during_war
			days = 30
		}
	}
}

# Effect to apply war restrictions on faction joining
apply_war_faction_restrictions = {
	if = {
		limit = {
			has_war = yes
			is_ai = yes
		}
		# Reduce faction joining desire to zero
		add_ai_strategy = {
			type = alliance
			id = "faction_joining"
			value = -1000
		}
	}
}
