# On actions to prevent AI faction joining during war

on_actions = {
	# Triggered when a country enters a war
	on_war_relation_added = {
		effect = {
			if = {
				limit = {
					is_ai = yes
				}
				# Add a country flag to prevent faction joining
				set_country_flag = at_war_no_faction_joining
				
				# Apply massive penalty to faction joining desire
				add_ai_strategy = {
					type = alliance
					id = "faction_joining_blocked"
					value = -10000
				}
			}
		}
	}
	
	# Triggered when a country exits all wars
	on_war_relation_removed = {
		effect = {
			if = {
				limit = {
					is_ai = yes
					NOT = { has_war = yes }
				}
				# Remove the flag when no longer at war
				clr_country_flag = at_war_no_faction_joining
				
				# Remove the penalty
				remove_ai_strategy = {
					type = alliance
					id = "faction_joining_blocked"
				}
			}
		}
	}
}
